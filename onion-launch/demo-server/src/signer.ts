import { Cell, beginCell } from '@ton/core';
import { sign, keyPairFromSeed } from '@ton/crypto';
import { PurchaseCalculation } from './types';
import { config } from './config';

export class SignatureService {
  private keyPair: any;

  constructor() {
    // Initialize key pair from private key
    // In production, use proper key management
    const seed = Buffer.from(config.signing_private_key, 'hex');
    this.keyPair = keyPairFromSeed(seed);
  }

  /**
   * Create a cell containing purchase calculation data
   */
  private createPurchaseCalculationCell(calc: PurchaseCalculation): Cell {
    return beginCell()
      .storeAddress(calc.user)
      .storeCoins(calc.amount)
      .storeUint(Number(calc.currency), 8)
      .storeCoins(calc.tokens_to_receive)
      .storeCoins(calc.current_price)
      .storeUint(Number(calc.current_round), 32)
      .storeUint(Number(calc.timestamp), 64)
      .storeUint(Number(calc.nonce), 64)
      .endCell();
  }

  /**
   * Hash purchase calculation data
   */
  private hashPurchaseCalculation(calc: PurchaseCalculation): Buffer {
    const cell = this.createPurchaseCalculationCell(calc);
    return cell.hash();
  }

  /**
   * Sign purchase calculation
   */
  signPurchaseCalculation(calc: PurchaseCalculation): string {
    const hash = this.hashPurchaseCalculation(calc);
    const signature = sign(hash, this.keyPair.secretKey);
    return signature.toString('base64');
  }

  /**
   * Verify signature (for testing purposes)
   */
  verifySignature(calc: PurchaseCalculation, signature: string): boolean {
    try {
      const hash = this.hashPurchaseCalculation(calc);
      const signatureBuffer = Buffer.from(signature, 'base64');
      
      // Note: This is a simplified verification
      // In production, use proper signature verification
      const expectedSignature = sign(hash, this.keyPair.secretKey);
      return signatureBuffer.equals(expectedSignature);
    } catch (error) {
      console.error('Signature verification error:', error);
      return false;
    }
  }

  /**
   * Get public key for contract configuration
   */
  getPublicKey(): string {
    return this.keyPair.publicKey.toString('hex');
  }

  /**
   * Get public key as BigInt for contract
   */
  getPublicKeyBigInt(): bigint {
    return BigInt('0x' + this.getPublicKey());
  }
}
