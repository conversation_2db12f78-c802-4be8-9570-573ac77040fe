import { Address, toNano, beginCell } from '@ton/core'
import { TonClient, WalletContractV4, internal } from '@ton/ton'
import { mnemonicToWalletKey } from '@ton/crypto'
import { 
    USDTPurchaseService, 
    createUSDTPurchaseWithSignature,
    buildUSDTForwardPayload,
    createPurchaseCalculation
} from '../frontend/src/lib/usdtPurchase'

// Configuration
const AUCTION_ADDRESS = 'EQC...' // Replace with actual auction contract address
const USDT_WALLET_ADDRESS = 'EQC...' // Replace with user's USDT wallet address
const API_BASE_URL = 'https://your-api-server.com' // Replace with your API server URL

async function main() {
    console.log('🚀 USDT Purchase with Signature Verification Example')
    console.log('================================================')

    // Initialize TON client
    const client = new TonClient({
        endpoint: 'https://toncenter.com/api/v2/jsonRPC',
        apiKey: 'your-api-key' // Get from @tonapibot
    })

    // Initialize wallet (replace with your mnemonic)
    const mnemonic = 'your wallet mnemonic words here...'
    const key = await mnemonicToWalletKey(mnemonic.split(' '))
    const wallet = WalletContractV4.create({ publicKey: key.publicKey, workchain: 0 })
    const walletContract = client.open(wallet)
    const walletSender = walletContract.sender(key.secretKey)

    // Initialize USDT purchase service
    const usdtService = new USDTPurchaseService(API_BASE_URL)

    try {
        // Example 1: Purchase with signature verification
        console.log('\n📝 Example 1: USDT Purchase with Signature Verification')
        console.log('-----------------------------------------------------')

        const usdtAmount = 50000000n // 50 USDT (6 decimals)
        const userAddress = wallet.address

        // Create purchase with signature
        const { calculation, signature, forwardPayload } = await createUSDTPurchaseWithSignature(
            userAddress,
            usdtAmount,
            usdtService
        )

        console.log('Purchase Calculation:')
        console.log(`  User: ${calculation.user.toString()}`)
        console.log(`  Amount: ${calculation.amount} (${Number(calculation.amount) / 1000000} USDT)`)
        console.log(`  Currency: ${calculation.currency} (USDT)`)
        console.log(`  Tokens to Receive: ${calculation.tokensToReceive}`)
        console.log(`  Current Price: ${calculation.currentPrice}`)
        console.log(`  Current Round: ${calculation.currentRound}`)
        console.log(`  Timestamp: ${calculation.timestamp}`)
        console.log(`  Nonce: ${calculation.nonce}`)
        console.log(`  Signature: ${signature.substring(0, 20)}...`)

        // Send USDT transfer with signature verification
        console.log('\n📤 Sending USDT transfer with signature verification...')
        
        const usdtTransfer = {
            $$type: 'JettonTransfer',
            query_id: 0n,
            amount: usdtAmount,
            destination: Address.parse(AUCTION_ADDRESS),
            response_destination: userAddress,
            custom_payload: null,
            forward_ton_amount: toNano('0.05'), // Enough gas for signature verification
            forward_payload: forwardPayload
        }

        // Note: In a real implementation, you would send this through the USDT wallet contract
        console.log('USDT Transfer Message:')
        console.log(JSON.stringify({
            to: USDT_WALLET_ADDRESS,
            value: toNano('0.1'),
            body: usdtTransfer
        }, null, 2))

        // Example 2: Direct USDT purchase (backward compatibility)
        console.log('\n📝 Example 2: Direct USDT Purchase (Backward Compatibility)')
        console.log('----------------------------------------------------------')

        const directUsdtTransfer = {
            $$type: 'JettonTransfer',
            query_id: 0n,
            amount: 25000000n, // 25 USDT
            destination: Address.parse(AUCTION_ADDRESS),
            response_destination: userAddress,
            custom_payload: null,
            forward_ton_amount: toNano('0.02'), // Less gas for direct purchase
            forward_payload: beginCell().endCell() // Empty payload for direct purchase
        }

        console.log('Direct USDT Transfer Message:')
        console.log(JSON.stringify({
            to: USDT_WALLET_ADDRESS,
            value: toNano('0.1'),
            body: directUsdtTransfer
        }, null, 2))

        // Example 3: Manual signature verification setup
        console.log('\n📝 Example 3: Manual Signature Verification Setup')
        console.log('------------------------------------------------')

        // Get current auction state
        const auctionState = await usdtService.getAuctionState()
        console.log('Current Auction State:')
        console.log(`  Current Price: ${auctionState.currentPrice}`)
        console.log(`  Current Round: ${auctionState.currentRound}`)
        console.log(`  Tokens Available: ${auctionState.tokensAvailable}`)

        // Calculate tokens manually
        const manualUsdtAmount = 100000000n // 100 USDT
        const tokensToReceive = usdtService.calculateTokensToReceive(manualUsdtAmount, auctionState.currentPrice)
        
        console.log(`\nFor ${Number(manualUsdtAmount) / 1000000} USDT:`)
        console.log(`  Tokens to receive: ${tokensToReceive}`)

        // Create calculation manually
        const manualCalculation = createPurchaseCalculation(
            userAddress,
            manualUsdtAmount,
            tokensToReceive,
            auctionState.currentPrice,
            auctionState.currentRound
        )

        // Get signature
        const manualSignature = await usdtService.getSignature(manualCalculation)
        
        // Build payload manually
        const manualForwardPayload = buildUSDTForwardPayload(manualCalculation, manualSignature)

        console.log('Manual setup completed successfully!')

        // Example 4: Error handling
        console.log('\n📝 Example 4: Error Handling Examples')
        console.log('------------------------------------')

        try {
            // Try to get signature for invalid calculation
            const invalidCalculation = createPurchaseCalculation(
                userAddress,
                0n, // Invalid amount
                tokensToReceive,
                auctionState.currentPrice,
                auctionState.currentRound
            )
            
            await usdtService.getSignature(invalidCalculation)
        } catch (error) {
            console.log('✅ Correctly caught invalid calculation error:', error.message)
        }

        console.log('\n✅ All examples completed successfully!')
        console.log('\n📚 Integration Guide:')
        console.log('1. Set up your API server to handle signature requests')
        console.log('2. Configure the signing key in your auction contract')
        console.log('3. Use USDTPurchaseService to get signatures for purchases')
        console.log('4. Build forward_payload with signature verification data')
        console.log('5. Send USDT transfer with the signed forward_payload')
        console.log('6. The contract will verify the signature and process the purchase')

    } catch (error) {
        console.error('❌ Error:', error)
    }
}

// Helper function to demonstrate the complete flow
async function demonstrateCompleteFlow() {
    console.log('\n🔄 Complete USDT Purchase Flow Demonstration')
    console.log('===========================================')

    const steps = [
        '1. User initiates USDT purchase on frontend',
        '2. Frontend gets current auction state from API',
        '3. Frontend calculates tokens to receive',
        '4. Frontend creates purchase calculation with nonce',
        '5. Frontend requests signature from API server',
        '6. API server validates calculation and signs it',
        '7. Frontend builds forward_payload with calculation + signature',
        '8. User sends USDT transfer with signed forward_payload',
        '9. USDT wallet forwards transfer to auction contract',
        '10. Auction contract receives JettonTransferNotification',
        '11. Contract parses forward_payload for signature data',
        '12. Contract verifies signature and nonce',
        '13. Contract processes purchase with pre-calculated values',
        '14. Contract creates/updates user purchase record',
        '15. Purchase completed successfully!'
    ]

    steps.forEach((step, index) => {
        console.log(`${step}`)
        if (index === 6 || index === 11) {
            console.log('   ↓')
        }
    })

    console.log('\n🔒 Security Benefits:')
    console.log('• Prevents price manipulation attacks')
    console.log('• Ensures calculation accuracy')
    console.log('• Prevents replay attacks with nonces')
    console.log('• Maintains backward compatibility')
    console.log('• Unified verification for TON and USDT')
}

if (require.main === module) {
    main().then(() => {
        demonstrateCompleteFlow()
    }).catch(console.error)
}

export { main, demonstrateCompleteFlow }
