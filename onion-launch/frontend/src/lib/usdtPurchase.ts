import { Address, beginCell, Cell, Slice } from '@ton/core'

// Purchase calculation data structure
export interface PurchaseCalculation {
    user: Address
    amount: bigint
    currency: number // 0=TON, 1=USDT
    tokensToReceive: bigint
    currentPrice: bigint
    currentRound: number
    timestamp: number
    nonce: bigint
}

// Server signature response
export interface SignatureResponse {
    calculation: PurchaseCalculation
    signature: string // Base64 encoded signature
}

// Build forward_payload for USDT purchase with signature verification
export function buildUSDTForwardPayload(calculation: PurchaseCalculation, signature: string): Cell {
    // Convert base64 signature to Slice
    const signatureBuffer = Buffer.from(signature, 'base64')
    const signatureCell = beginCell()
        .storeBuffer(signatureBuffer)
        .endCell()
    
    // Build the forward payload
    return beginCell()
        .storeAddress(calculation.user)
        .storeCoins(calculation.amount)
        .storeUint(calculation.currency, 8)
        .storeCoins(calculation.tokensToReceive)
        .storeCoins(calculation.currentPrice)
        .storeUint(calculation.currentRound, 32)
        .storeUint(calculation.timestamp, 64)
        .storeUint(Number(calculation.nonce), 64)
        .storeRef(signatureCell)
        .endCell()
}

// Generate a random nonce for anti-replay protection
export function generateNonce(): bigint {
    return BigInt(Math.floor(Math.random() * 1000000000000))
}

// Create purchase calculation object
export function createPurchaseCalculation(
    user: Address,
    usdtAmount: bigint,
    tokensToReceive: bigint,
    currentPrice: bigint,
    currentRound: number
): PurchaseCalculation {
    return {
        user,
        amount: usdtAmount,
        currency: 1, // USDT
        tokensToReceive,
        currentPrice,
        currentRound,
        timestamp: Math.floor(Date.now() / 1000),
        nonce: generateNonce()
    }
}

// API service for getting server signatures
export class USDTPurchaseService {
    constructor(private apiBaseUrl: string) {}

    // Get signature from server for purchase calculation
    async getSignature(calculation: PurchaseCalculation): Promise<string> {
        const response = await fetch(`${this.apiBaseUrl}/api/purchase/sign`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                user: calculation.user.toString(),
                amount: calculation.amount.toString(),
                currency: calculation.currency,
                tokensToReceive: calculation.tokensToReceive.toString(),
                currentPrice: calculation.currentPrice.toString(),
                currentRound: calculation.currentRound,
                timestamp: calculation.timestamp,
                nonce: calculation.nonce.toString()
            })
        })

        if (!response.ok) {
            throw new Error(`Failed to get signature: ${response.statusText}`)
        }

        const data = await response.json()
        return data.signature
    }

    // Get current auction state for calculation
    async getAuctionState(): Promise<{
        currentPrice: bigint
        currentRound: number
        tokensAvailable: bigint
    }> {
        const response = await fetch(`${this.apiBaseUrl}/api/auction/state`)
        
        if (!response.ok) {
            throw new Error(`Failed to get auction state: ${response.statusText}`)
        }

        const data = await response.json()
        return {
            currentPrice: BigInt(data.currentPrice),
            currentRound: data.currentRound,
            tokensAvailable: BigInt(data.tokensAvailable)
        }
    }

    // Calculate tokens to receive for USDT amount
    calculateTokensToReceive(usdtAmount: bigint, currentPrice: bigint): bigint {
        // Convert USDT (6 decimals) to TON equivalent (9 decimals)
        const usdtInTonUnits = usdtAmount * 1000n
        
        // Calculate tokens using the same formula as the contract
        return (usdtInTonUnits * 1000000000n) / currentPrice
    }
}

// Example usage helper
export async function createUSDTPurchaseWithSignature(
    user: Address,
    usdtAmount: bigint,
    apiService: USDTPurchaseService
): Promise<{ calculation: PurchaseCalculation; signature: string; forwardPayload: Cell }> {
    // Get current auction state
    const auctionState = await apiService.getAuctionState()
    
    // Calculate tokens to receive
    const tokensToReceive = apiService.calculateTokensToReceive(usdtAmount, auctionState.currentPrice)
    
    // Create purchase calculation
    const calculation = createPurchaseCalculation(
        user,
        usdtAmount,
        tokensToReceive,
        auctionState.currentPrice,
        auctionState.currentRound
    )
    
    // Get signature from server
    const signature = await apiService.getSignature(calculation)
    
    // Build forward payload
    const forwardPayload = buildUSDTForwardPayload(calculation, signature)
    
    return {
        calculation,
        signature,
        forwardPayload
    }
}
