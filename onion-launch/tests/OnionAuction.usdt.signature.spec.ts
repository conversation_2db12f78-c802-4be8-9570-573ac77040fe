import { Blockchain, SandboxContract, TreasuryContract } from '@ton/sandbox';
import { Cell, toNano, beginCell, Address } from '@ton/core';
import { OnionAuction } from '../wrappers/OnionAuction';
import { JettonMaster } from '../wrappers/JettonMaster';
import { JettonWallet } from '../wrappers/JettonWallet';
import '@ton/test-utils';
import { compile } from '@ton/blueprint';
import { sign } from '@ton/crypto';

describe('OnionAuction USDT Signature Verification', () => {
    let code: Cell;
    let jettonMasterCode: Cell;
    let jettonWalletCode: Cell;

    beforeAll(async () => {
        code = await compile('OnionAuction');
        jettonMasterCode = await compile('JettonMaster');
        jettonWalletCode = await compile('JettonWallet');
    });

    let blockchain: Blockchain;
    let deployer: SandboxContract<TreasuryContract>;
    let buyer: SandboxContract<TreasuryContract>;
    let onionAuction: SandboxContract<OnionAuction>;
    let usdtMaster: SandboxContract<JettonMaster>;
    let usdtWallet: SandboxContract<JettonWallet>;
    let signingKeyPair: { publicKey: Buffer; secretKey: Buffer };

    beforeEach(async () => {
        blockchain = await Blockchain.create();
        deployer = await blockchain.treasury('deployer');
        buyer = await blockchain.treasury('buyer');

        // Generate signing key pair for testing
        signingKeyPair = {
            publicKey: Buffer.from('test_public_key_32_bytes_long_123456', 'utf8').subarray(0, 32),
            secretKey: Buffer.from('test_secret_key_32_bytes_long_123456', 'utf8').subarray(0, 32)
        };

        // Deploy OnionAuction
        const startTime = Math.floor(Date.now() / 1000);
        const endTime = startTime + 3600; // 1 hour

        onionAuction = blockchain.openContract(OnionAuction.createFromConfig({
            owner: deployer.address,
            startTime,
            endTime,
            softCap: toNano('1000'),
            hardCap: toNano('10000'),
            totalSupply: toNano('1000000')
        }, code));

        const deployResult = await onionAuction.sendDeploy(deployer.getSender(), toNano('0.05'));
        expect(deployResult.transactions).toHaveTransaction({
            from: deployer.address,
            to: onionAuction.address,
            deploy: true,
            success: true,
        });

        // Start auction
        await onionAuction.sendStartAuction(deployer.getSender(), {
            value: toNano('0.05'),
            startTime,
            endTime,
            softCap: toNano('1000'),
            hardCap: toNano('10000'),
            initialPrice: toNano('0.1')
        });

        // Set signing key
        const publicKeyInt = BigInt('0x' + signingKeyPair.publicKey.toString('hex'));
        await onionAuction.sendSetSigningKey(deployer.getSender(), {
            value: toNano('0.05'),
            publicKey: publicKeyInt
        });

        // Deploy USDT Master
        usdtMaster = blockchain.openContract(JettonMaster.createFromConfig({
            admin: deployer.address,
            content: beginCell().endCell(),
            walletCode: jettonWalletCode
        }, jettonMasterCode));

        await usdtMaster.sendDeploy(deployer.getSender(), toNano('0.05'));

        // Set USDT configuration
        await onionAuction.sendSetUSDTAddress(deployer.getSender(), {
            value: toNano('0.05'),
            usdtMaster: usdtMaster.address,
            usdtWallet: null
        });

        // Get USDT wallet for buyer
        const usdtWalletAddress = await usdtMaster.getWalletAddress(buyer.address);
        usdtWallet = blockchain.openContract(JettonWallet.createFromAddress(usdtWalletAddress));

        // Mint USDT to buyer
        await usdtMaster.sendMint(deployer.getSender(), {
            value: toNano('0.1'),
            to: buyer.address,
            amount: 1000000000n // 1000 USDT
        });
    });

    it('should handle USDT purchase with signature verification', async () => {
        const usdtAmount = 50000000n; // 50 USDT
        const currentPrice = toNano('0.1');
        const currentRound = 1;
        const timestamp = Math.floor(Date.now() / 1000);
        const nonce = 12345n;

        // Calculate tokens to receive (same as contract logic)
        const usdtInTonUnits = usdtAmount * 1000n; // Convert 6 decimals to 9 decimals
        const tokensToReceive = (usdtInTonUnits * toNano('1')) / currentPrice;

        // Create purchase calculation
        const purchaseCalculation = {
            user: buyer.address,
            amount: usdtAmount,
            currency: 1, // USDT
            tokensToReceive,
            currentPrice,
            currentRound,
            timestamp,
            nonce
        };

        // Create data cell for signing (same as contract hashPurchaseCalculation)
        const dataCell = beginCell()
            .storeAddress(purchaseCalculation.user)
            .storeCoins(purchaseCalculation.amount)
            .storeUint(purchaseCalculation.currency, 8)
            .storeCoins(purchaseCalculation.tokensToReceive)
            .storeCoins(purchaseCalculation.currentPrice)
            .storeUint(purchaseCalculation.currentRound, 32)
            .storeUint(purchaseCalculation.timestamp, 64)
            .storeUint(Number(purchaseCalculation.nonce), 64)
            .endCell();

        // Sign the data
        const dataHash = dataCell.hash();
        const signature = sign(dataHash, signingKeyPair.secretKey);

        // Build forward_payload
        const forwardPayload = beginCell()
            .storeAddress(purchaseCalculation.user)
            .storeCoins(purchaseCalculation.amount)
            .storeUint(purchaseCalculation.currency, 8)
            .storeCoins(purchaseCalculation.tokensToReceive)
            .storeCoins(purchaseCalculation.currentPrice)
            .storeUint(purchaseCalculation.currentRound, 32)
            .storeUint(purchaseCalculation.timestamp, 64)
            .storeUint(Number(purchaseCalculation.nonce), 64)
            .storeRef(beginCell().storeBuffer(signature).endCell())
            .endCell();

        // Send USDT transfer with signature verification
        const purchaseResult = await onionAuction.send(
            usdtWallet.getSender(),
            { value: toNano('0.2') },
            {
                $$type: 'JettonTransferNotification',
                query_id: 0n,
                amount: usdtAmount,
                sender: buyer.address,
                forward_payload: forwardPayload
            }
        );

        expect(purchaseResult.transactions).toHaveTransaction({
            from: usdtWallet.address,
            to: onionAuction.address,
            success: true,
        });

        // Verify purchase was processed
        const totalRaisedUSDT = await onionAuction.getTotalRaisedUsdt();
        expect(totalRaisedUSDT).toBe(usdtAmount);

        const totalTokensSold = await onionAuction.getTotalTokensSold();
        expect(totalTokensSold).toBe(tokensToReceive);
    });

    it('should reject USDT purchase with invalid signature', async () => {
        const usdtAmount = 50000000n;
        const currentPrice = toNano('0.1');
        const currentRound = 1;
        const timestamp = Math.floor(Date.now() / 1000);
        const nonce = 12346n;

        const usdtInTonUnits = usdtAmount * 1000n;
        const tokensToReceive = (usdtInTonUnits * toNano('1')) / currentPrice;

        // Create purchase calculation
        const purchaseCalculation = {
            user: buyer.address,
            amount: usdtAmount,
            currency: 1,
            tokensToReceive,
            currentPrice,
            currentRound,
            timestamp,
            nonce
        };

        // Create invalid signature (wrong data)
        const wrongDataCell = beginCell()
            .storeAddress(purchaseCalculation.user)
            .storeCoins(purchaseCalculation.amount + 1n) // Wrong amount
            .storeUint(purchaseCalculation.currency, 8)
            .storeCoins(purchaseCalculation.tokensToReceive)
            .storeCoins(purchaseCalculation.currentPrice)
            .storeUint(purchaseCalculation.currentRound, 32)
            .storeUint(purchaseCalculation.timestamp, 64)
            .storeUint(Number(purchaseCalculation.nonce), 64)
            .endCell();

        const wrongDataHash = wrongDataCell.hash();
        const invalidSignature = sign(wrongDataHash, signingKeyPair.secretKey);

        // Build forward_payload with invalid signature
        const forwardPayload = beginCell()
            .storeAddress(purchaseCalculation.user)
            .storeCoins(purchaseCalculation.amount)
            .storeUint(purchaseCalculation.currency, 8)
            .storeCoins(purchaseCalculation.tokensToReceive)
            .storeCoins(purchaseCalculation.currentPrice)
            .storeUint(purchaseCalculation.currentRound, 32)
            .storeUint(purchaseCalculation.timestamp, 64)
            .storeUint(Number(purchaseCalculation.nonce), 64)
            .storeRef(beginCell().storeBuffer(invalidSignature).endCell())
            .endCell();

        // Send USDT transfer with invalid signature
        const purchaseResult = await onionAuction.send(
            usdtWallet.getSender(),
            { value: toNano('0.2') },
            {
                $$type: 'JettonTransferNotification',
                query_id: 0n,
                amount: usdtAmount,
                sender: buyer.address,
                forward_payload: forwardPayload
            }
        );

        expect(purchaseResult.transactions).toHaveTransaction({
            from: usdtWallet.address,
            to: onionAuction.address,
            success: false,
        });
    });

    it('should handle direct USDT purchase without signature (backward compatibility)', async () => {
        const usdtAmount = 25000000n; // 25 USDT

        // Send USDT transfer without forward_payload (direct purchase)
        const purchaseResult = await onionAuction.send(
            usdtWallet.getSender(),
            { value: toNano('0.2') },
            {
                $$type: 'JettonTransferNotification',
                query_id: 0n,
                amount: usdtAmount,
                sender: buyer.address,
                forward_payload: beginCell().endCell() // Empty payload
            }
        );

        expect(purchaseResult.transactions).toHaveTransaction({
            from: usdtWallet.address,
            to: onionAuction.address,
            success: true,
        });

        // Verify purchase was processed with direct calculation
        const totalRaisedUSDT = await onionAuction.getTotalRaisedUsdt();
        expect(totalRaisedUSDT).toBe(usdtAmount);
    });

    it('should reject replay attacks with used nonce', async () => {
        const usdtAmount = 30000000n;
        const currentPrice = toNano('0.1');
        const currentRound = 1;
        const timestamp = Math.floor(Date.now() / 1000);
        const nonce = 12347n;

        const usdtInTonUnits = usdtAmount * 1000n;
        const tokensToReceive = (usdtInTonUnits * toNano('1')) / currentPrice;

        const purchaseCalculation = {
            user: buyer.address,
            amount: usdtAmount,
            currency: 1,
            tokensToReceive,
            currentPrice,
            currentRound,
            timestamp,
            nonce
        };

        const dataCell = beginCell()
            .storeAddress(purchaseCalculation.user)
            .storeCoins(purchaseCalculation.amount)
            .storeUint(purchaseCalculation.currency, 8)
            .storeCoins(purchaseCalculation.tokensToReceive)
            .storeCoins(purchaseCalculation.currentPrice)
            .storeUint(purchaseCalculation.currentRound, 32)
            .storeUint(purchaseCalculation.timestamp, 64)
            .storeUint(Number(purchaseCalculation.nonce), 64)
            .endCell();

        const dataHash = dataCell.hash();
        const signature = sign(dataHash, signingKeyPair.secretKey);

        const forwardPayload = beginCell()
            .storeAddress(purchaseCalculation.user)
            .storeCoins(purchaseCalculation.amount)
            .storeUint(purchaseCalculation.currency, 8)
            .storeCoins(purchaseCalculation.tokensToReceive)
            .storeCoins(purchaseCalculation.currentPrice)
            .storeUint(purchaseCalculation.currentRound, 32)
            .storeUint(purchaseCalculation.timestamp, 64)
            .storeUint(Number(purchaseCalculation.nonce), 64)
            .storeRef(beginCell().storeBuffer(signature).endCell())
            .endCell();

        // First purchase should succeed
        const firstPurchase = await onionAuction.send(
            usdtWallet.getSender(),
            { value: toNano('0.2') },
            {
                $$type: 'JettonTransferNotification',
                query_id: 0n,
                amount: usdtAmount,
                sender: buyer.address,
                forward_payload: forwardPayload
            }
        );

        expect(firstPurchase.transactions).toHaveTransaction({
            from: usdtWallet.address,
            to: onionAuction.address,
            success: true,
        });

        // Second purchase with same nonce should fail
        const secondPurchase = await onionAuction.send(
            usdtWallet.getSender(),
            { value: toNano('0.2') },
            {
                $$type: 'JettonTransferNotification',
                query_id: 0n,
                amount: usdtAmount,
                sender: buyer.address,
                forward_payload: forwardPayload
            }
        );

        expect(secondPurchase.transactions).toHaveTransaction({
            from: usdtWallet.address,
            to: onionAuction.address,
            success: false,
        });
    });
});
